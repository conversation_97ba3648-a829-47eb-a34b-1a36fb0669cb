# 电子签名系统核心实现文档

## 系统概述

本文档描述了企业审批系统中电子签名在申请书模板对应位置显示的核心实现逻辑。系统采用Node.js + Express后端，HTML/CSS/JavaScript前端的架构。

## 核心功能

### 1. 电子签名存储机制

#### 数据结构
```javascript
// 用户数据结构 (users.json)
{
  "username": "张三",
  "role": "director",
  "signature": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..." // Base64编码的签名图片
}

// 申请数据结构 (applications.json)
{
  "id": 1742345334537,
  "applicant": "余焕新",
  "status": "已通过",
  "approvals": {
    "directors": {
      "李奇伟": {
        "status": "approved",
        "signature": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
        "date": "2025-03-19T01:05:57.332Z"
      }
    },
    "chief": {
      "status": "approved", 
      "signature": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAAAAAAAD...",
      "date": "2025-03-24T06:16:03.875Z"
    }
  }
}
```

#### 签名获取优先级
1. 审批记录中的签名 (`app.approvals.directors[username].signature`)
2. 用户数据中的签名 (`user.signature`)
3. 显示"无签名"占位符

### 2. 审批流程设计

#### 多级审批架构
```
申请提交 → 厂长审批 → 总监审批 → 经理审批 → CEO审批 → 完成
```

#### 状态管理
- `待厂长审核` - 等待指定厂长审批
- `待总监审批` - 等待总监审批  
- `待经理审批` - 等待指定经理审批
- `待CEO审批` - 等待CEO最终审批
- `已通过` - 审批流程完成
- `已拒绝` - 申请被拒绝

### 3. 申请书模板生成

#### 核心模板结构
```html
<div class="application-template">
  <!-- 申请基本信息 -->
  <div class="application-header">
    <h1>申请书</h1>
    <div class="application-info">
      <p>申请人：${application.applicant}</p>
      <p>申请日期：${formatDate(application.date)}</p>
      <p>申请编号：${application.applicationCode}</p>
    </div>
  </div>

  <!-- 申请内容 -->
  <div class="application-content">
    <h3>申请内容：</h3>
    <div class="content-text">${application.content}</div>
  </div>

  <!-- 审批签名区域 -->
  <div class="approval-signatures">
    <!-- 厂长审批区域 -->
    <div class="approval-section">
      <h4>厂长核准：</h4>
      <div class="signature-area">${directorApproval}</div>
    </div>

    <!-- 总监审批区域 -->
    <div class="approval-section">
      <h4>总监核准：</h4>
      <div class="signature-area">${chiefApproval}</div>
    </div>

    <!-- 经理审批区域 -->
    <div class="approval-section">
      <h4>经理核准：</h4>
      <div class="signature-area">${managerApproval}</div>
    </div>
  </div>
</div>
```

### 4. 签名显示逻辑实现

#### JavaScript核心函数
```javascript
// 生成申请书模板的核心函数
function generateApplicationTemplate(application) {
    // 生成厂长审批签名
    let directorApproval = '';
    if (application.approvals && application.approvals.directors) {
        Object.entries(application.approvals.directors).forEach(([username, approval]) => {
            if (approval.status === 'approved' || approval.status === 'rejected') {
                // 获取签名 - 优先使用审批记录中的签名
                const signature = approval.signature || getUserSignature(username);
                
                directorApproval += `
                    <div class="single-approval">
                        <div class="signature-wrapper">
                            ${signature ? 
                                `<img src="${signature}" alt="${username}的签名" class="signature-image">` :
                                `<div class="no-signature">无签名</div>`
                            }
                            <div class="approval-date">${formatDate(approval.date)}</div>
                        </div>
                    </div>
                `;
            }
        });
    }

    // 生成总监审批签名
    let chiefApproval = '';
    if (application.approvals && application.approvals.chief && 
        (application.approvals.chief.status === 'approved' || application.approvals.chief.status === 'rejected')) {
        
        const signature = application.approvals.chief.signature || getChiefSignature();
        
        chiefApproval = `
            <div class="signature-wrapper">
                ${signature ? 
                    `<img src="${signature}" alt="总监签名" class="signature-image">` :
                    `<div class="no-signature">无签名</div>`
                }
                <div class="approval-date">${formatDate(application.approvals.chief.date)}</div>
            </div>
        `;
    }

    // 生成经理审批签名（类似逻辑）
    let managerApproval = generateManagerApproval(application);

    // 组装完整模板
    const template = `
        <div class="application-template">
            ${generateHeader(application)}
            ${generateContent(application)}
            <div class="approval-signatures">
                <div class="approval-section">
                    <h4>厂长核准：</h4>
                    ${directorApproval}
                </div>
                <div class="approval-section">
                    <h4>总监核准：</h4>
                    ${chiefApproval}
                </div>
                <div class="approval-section">
                    <h4>经理核准：</h4>
                    ${managerApproval}
                </div>
            </div>
        </div>
    `;

    return template;
}

// 获取用户签名的辅助函数
function getUserSignature(username) {
    const user = allUsers.find(u => u.username === username);
    return user ? user.signature : null;
}

// 日期格式化函数
function formatDate(dateString) {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${year}年${month}月${day}日`;
}
```

### 5. CSS样式设计

#### 签名显示样式
```css
/* 审批容器样式 */
.approval-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin: 10px 0;
}

.single-approval {
    border: 1px solid #ddd;
    padding: 10px;
    border-radius: 5px;
    min-width: 150px;
    text-align: center;
    font-size: 14px;
}

.signature-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* 签名图片样式 */
.signature-image {
    max-width: 120px;
    max-height: 60px;
    margin-bottom: 5px;
    border: 1px solid #eee;
    border-radius: 3px;
    image-rendering: -webkit-optimize-contrast;
    filter: contrast(1.05);
}

/* 无签名占位符样式 */
.no-signature {
    color: #666;
    font-style: italic;
    font-size: 12px;
    margin-bottom: 5px;
    font-weight: 500;
}

/* 审批日期样式 */
.approval-date {
    color: #666;
    font-size: 10px;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .signature-image {
        max-width: 100px;
        max-height: 50px;
    }
    
    .no-signature {
        font-size: 11px;
    }
}

/* 打印样式 */
@media print {
    .signature-image {
        max-width: 120px;
        max-height: 60px;
        print-color-adjust: exact;
        -webkit-print-color-adjust: exact;
    }
}
```

### 6. 后端API接口

#### 签名获取接口
```javascript
// 获取所有用户签名信息
app.get('/signatures', (req, res) => {
    try {
        const users = JSON.parse(fs.readFileSync(usersFile, 'utf8'));
        const signatures = users.map(user => ({
            username: user.username,
            role: user.role,
            signature: user.signature || null
        }));
        res.json(signatures);
    } catch (error) {
        console.error('获取签名数据失败:', error);
        res.status(500).json({ success: false, message: '获取签名数据失败' });
    }
});

// 审批接口（包含签名处理）
app.post('/approve', (req, res) => {
    const { id, action, comment, signature, selectedManagers } = req.body;
    const username = req.body.username;
    
    try {
        const applications = JSON.parse(fs.readFileSync(applicationsFile, 'utf8'));
        const appIndex = applications.findIndex(app => app.id === parseInt(id));
        
        if (appIndex === -1) {
            return res.status(404).json({ success: false, message: '申请不存在' });
        }

        const app = applications[appIndex];
        
        // 根据用户角色处理审批
        if (userRole === 'director') {
            app.approvals.directors[username] = {
                status: action,
                comment: comment || '',
                date: new Date().toISOString(),
                signature: signature || '' // 保存签名
            };
        } else if (userRole === 'chief') {
            app.approvals.chief = {
                status: action,
                comment: comment || '',
                date: new Date().toISOString(),
                signature: signature || '' // 保存签名
            };
        }
        // ... 其他角色处理逻辑

        // 保存更新后的数据
        fs.writeFileSync(applicationsFile, JSON.stringify(applications, null, 2));
        
        res.json({ success: true, message: '审批成功' });
    } catch (error) {
        console.error('审批处理失败:', error);
        res.status(500).json({ success: false, message: '审批处理失败' });
    }
});
```

## 实现要点

### 1. 签名数据管理
- 使用Base64格式存储图片数据
- 支持多数据源签名获取
- 实现签名缓存机制

### 2. 模板动态生成
- 根据审批状态动态显示签名
- 支持多种设备适配
- 优化打印显示效果

### 3. 性能优化
- 图片懒加载
- 签名数据缓存
- 移动端优化

### 4. 安全考虑
- 输入数据验证
- XSS防护
- 文件上传限制

## 关键代码示例

### 前端签名处理
```javascript
// 从申请数据中提取签名信息
function extractSignaturesFromApplications() {
    if (!applications || applications.length === 0) return;

    allUsers = allUsers || [];

    applications.forEach(app => {
        if (app.approvals) {
            // 提取厂长签名
            if (app.approvals.directors) {
                Object.entries(app.approvals.directors).forEach(([username, approval]) => {
                    if (approval.signature) {
                        const existingUser = allUsers.find(u => u.username === username);
                        if (existingUser) {
                            if (!existingUser.signature) {
                                existingUser.signature = approval.signature;
                            }
                        } else {
                            allUsers.push({
                                username: username,
                                role: 'director',
                                signature: approval.signature
                            });
                        }
                    }
                });
            }

            // 提取总监签名
            if (app.approvals.chief && app.approvals.chief.signature) {
                const chiefUsername = app.approvals.chief.username || 'chief';
                const existingChief = allUsers.find(u => u.username === chiefUsername || u.role === 'chief');

                if (existingChief) {
                    if (!existingChief.signature) {
                        existingChief.signature = app.approvals.chief.signature;
                    }
                } else {
                    allUsers.push({
                        username: chiefUsername,
                        role: 'chief',
                        signature: app.approvals.chief.signature
                    });
                }
            }
        }
    });
}

// 签名图片优化
function optimizeImage(img) {
    img.style.imageRendering = '-webkit-optimize-contrast';
    img.style.filter = 'contrast(1.05)';
    img.setAttribute('loading', 'lazy');
}
```

### 后端文件处理
```javascript
const express = require('express');
const multer = require('multer');
const fs = require('fs');
const path = require('path');

const app = express();

// 配置文件上传
const upload = multer({
    limits: { fileSize: 5 * 1024 * 1024 }, // 5MB限制
    fileFilter: (req, file, cb) => {
        if (file.mimetype.startsWith('image/')) {
            cb(null, true);
        } else {
            cb(new Error('只允许上传图片文件'));
        }
    }
});

// 签名验证中间件
function validateSignature(signature) {
    if (!signature) return true; // 允许空签名

    // 检查是否为有效的base64图片格式
    const base64Regex = /^data:image\/(png|jpeg|jpg|gif);base64,/;
    return base64Regex.test(signature);
}
```

## 数据流程图

```
用户上传签名图片 → Base64编码 → 存储到用户数据
                                    ↓
审批操作 → 获取用户签名 → 保存到审批记录 → 模板生成时显示
                                    ↓
申请书模板 ← 动态获取签名 ← 多数据源签名查找
```

## 部署说明

1. 确保Node.js环境
2. 安装依赖：`npm install express multer`
3. 创建数据目录：`backend/data/`
4. 启动服务：`node backend/server.js`
5. 访问：`http://localhost:3000`

## 总结

这个精简方案专注于电子签名显示的核心逻辑，包含：
- 签名存储与获取机制
- 动态模板生成逻辑
- 多级审批签名显示
- 响应式设计适配

易于理解和实现，适合快速复刻签名功能。
